2025/07/30 14:36:39 === Test Session Started at 2025-07-30 14:36:39 ===
2025/07/30 14:36:39 +++++++++++++++ Starting test for dev1 +++++++++++++++
2025/07/30 14:36:39 Testing environment: dev1
2025/07/30 14:36:39 External URL: http://ny10wt9045294.vicp.fun:25639
2025/07/30 14:36:39 Local Gin URL: http://192.168.1.200:15445
2025/07/30 14:36:39 Local RPC URL: http://192.168.1.200:15446
2025/07/30 14:36:39 
2025/07/30 14:36:39 === Getting External API Token ===
2025/07/30 14:36:39 Token obtained successfully: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************.cw4rxR-vAEk-xjK4Q4zLq0j7w3Za8EdsuqKHkqv1t-c
2025/07/30 14:36:39 --- Admin Add Packages (Internal Dubbo RPC) ---
2025/07/30 14:36:39 URL: http://192.168.1.200:15446/com.aibook.storepb.grpc.StoreService/AdminAddPackages
2025/07/30 14:36:39 Headers: map[x-trace-id:test-1753857399275775000-1753857399]
2025/07/30 14:36:39 Request Body: {PackageName:RPC测试流量包 PackageDesc:用于RPC API测试的流量包 PackageType:traffic Entitlement:200 EntitlementDesc:200GB流量 OriginalPrice:29.99 DiscountPrice:<nil> DiscountStartTime:<nil> DiscountEndTime:<nil> DiscountDesc: SaleStatus:on_sale Currency:USD Country:US PSPProductID: PSPProductDesc: PSPPriceID: PSPPriceDesc: ProductImg: Extra1:test Extra2:rpc Extra3:100 Extra4:30}
2025/07/30 14:36:40 Response Status: 200
2025/07/30 14:36:40 Response Body: {
      "package_id": "4ca99865-4616-4194-af23-554290c58c15",
      "package_name": "RPC测试流量包",
      "package_desc": "用于RPC API测试的流量包",
      "package_type": "traffic",
      "entitlement": 200,
      "entitlement_desc": "200GB流量",
      "original_price": 29.99,
      "sale_status": "on_sale",
      "currency": "USD",
      "country": "US",
      "psp_product_id": "prod_Sm1gxHY6TpNmR1",
      "psp_price_id": "price_1RqTczC53MAl6WmqDP5L5bL1",
      "extra1": "test",
      "extra2": "rpc",
      "extra3": 100,
      "extra4": 30,
      "created_at": "2025-07-30T06:36:41.763550550Z",
      "updated_at": "2025-07-30T06:36:41.763550550Z"
    }
2025/07/30 14:36:40 --- Admin List All Packages (Internal Dubbo RPC) ---
2025/07/30 14:36:40 URL: http://192.168.1.200:15446/com.aibook.storepb.grpc.StoreService/AdminListAllPackages
2025/07/30 14:36:40 Headers: map[x-trace-id:test-1753857400461702700-1753857400]
2025/07/30 14:36:40 Request Body: map[pagination:map[limit:50 offset:0]]
2025/07/30 14:36:40 Response Status: 200
2025/07/30 14:36:40 Response Body: {
      "packages": [
        {
          "id": "84",
          "package_id": "4ca99865-4616-4194-af23-554290c58c15",
          "package_name": "RPC测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "original_price": 29.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_Sm1gxHY6TpNmR1",
          "psp_price_id": "price_1RqTczC53MAl6WmqDP5L5bL1",
          "extra1": "test",
          "extra2": "rpc",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-30T06:36:41.764Z",
          "updated_at": "2025-07-30T06:36:41.764Z"
        },
        {
          "id": "78",
          "package_id": "cfeb107d-935f-498e-ba1e-694259f867ef",
          "package_name": "RPC更新后的测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "original_price": 124.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfzrbIN7jZQmv",
          "psp_price_id": "price_1Rq8dGC53MAl6WmqIxjKOJ0S",
          "extra1": "test",
          "extra2": "rpc",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-29T08:11:33.831Z",
          "updated_at": "2025-07-29T08:11:35.232Z"
        },
        {
          "id": "75",
          "package_id": "3829b0a6-3f24-4a92-8af8-166593853f48",
          "package_name": "更新后的测试流量包",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "original_price": 24.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfVgN67pzHvKO",
          "psp_price_id": "price_1Rq8AgC53MAl6WmqDpb4uRMg",
          "extra1": "test",
          "extra2": "api",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-29T07:42:01.766Z",
          "updated_at": "2025-07-29T07:42:03.349Z"
        },
        {
          "id": "74",
          "package_id": "70579727-8354-46db-ae9d-6c3c3d3b2324",
          "package_name": "RPC更新后的测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "original_price": 124.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfUCd639uIllj",
          "psp_price_id": "price_1Rq89EC53MAl6WmqiO6QMx1A",
          "extra1": "test",
          "extra2": "rpc",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-29T07:40:31.444Z",
          "updated_at": "2025-07-29T07:40:32.895Z"
        },
        {
          "id": "73",
          "package_id": "9dd69850-878d-462b-803a-8340de16e41d",
          "package_name": "更新后的测试流量包",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "original_price": 24.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfUX7NuQFJjh0",
          "psp_price_id": "price_1Rq89BC53MAl6WmqIpzrl4Yl",
          "extra1": "test",
          "extra2": "api",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-29T07:40:28.690Z",
          "updated_at": "2025-07-29T07:40:30.428Z"
        },
        {
          "id": "68",
          "package_id": "79d4d6ee-9cd8-4c7d-8447-b84fbafa6d9c",
          "package_name": "测试流量包GIN",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "original_price": 19.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleiBQSSXQTZ4i",
          "psp_price_id": "price_1Rq7OrC53MAl6WmqS5JQIarV",
          "extra1": "test",
          "extra2": "api",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-29T06:52:37.611Z",
          "updated_at": "2025-07-29T06:52:37.611Z"
        },
        {
          "id": "65",
          "package_id": "5490af08-49c0-4732-96fa-fa411725c09c",
          "package_name": "Test_Package_1GB",
          "package_desc": "Test data package",
          "entitlement": 1024,
          "entitlement_desc": "1GB data",
          "original_price": 9.99,
          "discount_price": 7.99,
          "discount_start_time": "2025-01-01T00:00:00Z",
          "discount_end_time": "2025-01-02T00:00:00Z",
          "discount_desc": "Test discount",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleNjyyVfYdAFp",
          "psp_price_id": "price_1Rq75CC53MAl6WmqJ1L2PrMk",
          "extra1": "test",
          "extra2": "test",
          "extra3": 1,
          "extra4": 2,
          "created_at": "2025-07-29T06:32:19.219Z",
          "updated_at": "2025-07-29T06:32:19.219Z"
        },
        {
          "id": "62",
          "package_id": "b0b427a0-2b5f-45c6-9450-d49d1cd6c876",
          "package_name": "RPC更新后的测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "original_price": 124.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleDhg8ZZI15zj",
          "psp_price_id": "price_1Rq6vVC53MAl6WmqYxS3dnxB",
          "extra1": "test",
          "extra2": "rpc",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-29T06:22:17.189Z",
          "updated_at": "2025-07-29T06:22:18.538Z"
        },
        {
          "id": "61",
          "package_id": "100f3dd9-2b06-4af7-ab75-51f69427e22e",
          "package_name": "更新后的测试流量包",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "original_price": 24.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleDddaMGUabUQ",
          "psp_price_id": "price_1Rq6vTC53MAl6WmqTkmCgEFb",
          "extra1": "test",
          "extra2": "api",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-29T06:22:14.902Z",
          "updated_at": "2025-07-29T06:22:16.252Z"
        },
        {
          "id": "58",
          "package_id": "9da2481e-5d73-4621-aa9b-706c7f22e4ef",
          "package_name": "RPC测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "original_price": 29.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_Sle1DdqNy9Sm7b",
          "psp_price_id": "price_1Rq6jLC53MAl6WmqdCNbU2n9",
          "extra1": "test",
          "extra2": "rpc",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-29T06:09:43.306Z",
          "updated_at": "2025-07-29T06:09:43.306Z"
        },
        {
          "id": "57",
          "package_id": "ef59713c-3943-4efd-be88-0f9c43ba1f30",
          "package_name": "测试流量包GIN",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "original_price": 19.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_Sle1aEssOST7HY",
          "psp_price_id": "price_1Rq6jJC53MAl6WmqgzEKq7mf",
          "extra1": "test",
          "extra2": "api",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-29T06:09:42.157Z",
          "updated_at": "2025-07-29T06:09:42.157Z"
        },
        {
          "id": "50",
          "package_id": "a4d4a925-f9a2-4ba4-897b-2f82c54f83d8",
          "package_name": "pytest测试流量包",
          "package_desc": "pytest自动化测试创建的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "original_price": 39.99,
          "discount_desc": "pytest优惠",
          "sale_status": "on_sale",
          "currency": "CNY",
          "country": "CN",
          "psp_product_id": "prod_SlasmYGYBvnEvS",
          "psp_price_id": "price_1Rq3guC53MAl6Wmq5opbJepr",
          "extra1": "pytest",
          "extra2": "dubbo",
          "extra3": 123,
          "extra4": 456,
          "created_at": "2025-07-29T02:55:01.149Z",
          "updated_at": "2025-07-29T02:55:01.149Z"
        },
        {
          "id": "43",
          "package_id": "a22f526f-a34d-4b2a-a6f6-4e6a9aaf3229",
          "package_name": "RPC测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "original_price": 29.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlYpXswH5TWKRK",
          "psp_price_id": "price_1Rq1hmC53MAl6WmqWxRUMWjG",
          "extra1": "test",
          "extra2": "rpc",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-29T00:47:46.545Z",
          "updated_at": "2025-07-29T00:47:46.545Z"
        },
        {
          "id": "42",
          "package_id": "92e7c9e7-1950-452e-8488-711784d0ef8e",
          "package_name": "测试流量包GIN",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "original_price": 19.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlYpp1VAJYpLrI",
          "psp_price_id": "price_1Rq1hlC53MAl6Wmq6k1EspdD",
          "extra1": "test",
          "extra2": "api",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-29T00:47:45.438Z",
          "updated_at": "2025-07-29T00:47:45.438Z"
        },
        {
          "id": "28",
          "package_id": "a65f1502-5515-4d44-9429-3e61edf7e792",
          "package_name": "这是真实数据",
          "package_desc": "这是真实数据",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "original_price": 125.99,
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SWi3lfOWBSCgdU",
          "psp_price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "extra1": "test",
          "extra2": "rpc",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-26T01:53:10.315Z",
          "updated_at": "2025-07-28T03:45:38.995Z"
        }
      ],
      "pagination": {
        "total": "15",
        "limit": 50
      }
    }
2025/07/30 14:36:40 --- Admin Update Packages (Internal Dubbo RPC) ---
2025/07/30 14:36:40 URL: http://192.168.1.200:15446/com.aibook.storepb.grpc.StoreService/AdminUpdatePackages
2025/07/30 14:36:40 Headers: map[x-trace-id:test-1753857400540964900-1753857400]
2025/07/30 14:36:40 Request Body: {PackageID:4ca99865-4616-4194-af23-554290c58c15 PackageName:0xc0001084a0 PackageDesc:<nil> Entitlement:<nil> EntitlementDesc:<nil> OriginalPrice:0xc000102890 DiscountPrice:<nil> DiscountStartTime:<nil> DiscountEndTime:<nil> DiscountDesc:<nil> SaleStatus:<nil> Currency:<nil> Country:<nil> PSPProductID:<nil> PSPProductDesc:<nil> PSPPriceID:<nil> PSPPriceDesc:<nil> ProductImg:<nil> Extra1:<nil> Extra2:<nil> Extra3:<nil> Extra4:<nil>}
2025/07/30 14:36:41 Response Status: 200
2025/07/30 14:36:41 Response Body: {
      "message": "Package updated successfully"
    }
2025/07/30 14:36:41 --- List All Packages (External Gin HTTP) ---
2025/07/30 14:36:41 URL: http://ny10wt9045294.vicp.fun:25639/api/v1/pay-service/store-service/packages?limit=50&offset=0
2025/07/30 14:36:41 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************.cw4rxR-vAEk-xjK4Q4zLq0j7w3Za8EdsuqKHkqv1t-c x-trace-id:test-1753857401944425800-1753857401]
2025/07/30 14:36:42 Response Status: 200
2025/07/30 14:36:42 Response Body: {
      "packages": [
        {
          "package_id": "4ca99865-4616-4194-af23-554290c58c15",
          "package_name": "RPC更新后的测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 124.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_Sm1gxHY6TpNmR1",
          "psp_product_desc": "",
          "psp_price_id": "price_1RqTd0C53MAl6WmqGoWhTPOh",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "cfeb107d-935f-498e-ba1e-694259f867ef",
          "package_name": "RPC更新后的测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 124.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfzrbIN7jZQmv",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq8dGC53MAl6WmqIxjKOJ0S",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "3829b0a6-3f24-4a92-8af8-166593853f48",
          "package_name": "更新后的测试流量包",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 24.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfVgN67pzHvKO",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq8AgC53MAl6WmqDpb4uRMg",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "70579727-8354-46db-ae9d-6c3c3d3b2324",
          "package_name": "RPC更新后的测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 124.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfUCd639uIllj",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq89EC53MAl6WmqiO6QMx1A",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "9dd69850-878d-462b-803a-8340de16e41d",
          "package_name": "更新后的测试流量包",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 24.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfUX7NuQFJjh0",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq89BC53MAl6WmqIpzrl4Yl",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "79d4d6ee-9cd8-4c7d-8447-b84fbafa6d9c",
          "package_name": "测试流量包GIN",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 19.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleiBQSSXQTZ4i",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq7OrC53MAl6WmqS5JQIarV",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "5490af08-49c0-4732-96fa-fa411725c09c",
          "package_name": "Test_Package_1GB",
          "package_desc": "Test data package",
          "package_type": "traffic",
          "entitlement": 1024,
          "entitlement_desc": "1GB data",
          "price": 9.99,
          "original_price": null,
          "discount_start_time": "2025-01-01T08:00:00+08:00",
          "discount_end_time": "2025-01-02T08:00:00+08:00",
          "discount_desc": "Test discount",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleNjyyVfYdAFp",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq75CC53MAl6WmqJ1L2PrMk",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "b0b427a0-2b5f-45c6-9450-d49d1cd6c876",
          "package_name": "RPC更新后的测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 124.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleDhg8ZZI15zj",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq6vVC53MAl6WmqYxS3dnxB",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "100f3dd9-2b06-4af7-ab75-51f69427e22e",
          "package_name": "更新后的测试流量包",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 24.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleDddaMGUabUQ",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq6vTC53MAl6WmqTkmCgEFb",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "9da2481e-5d73-4621-aa9b-706c7f22e4ef",
          "package_name": "RPC测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 29.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_Sle1DdqNy9Sm7b",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq6jLC53MAl6WmqdCNbU2n9",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "ef59713c-3943-4efd-be88-0f9c43ba1f30",
          "package_name": "测试流量包GIN",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 19.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_Sle1aEssOST7HY",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq6jJC53MAl6WmqgzEKq7mf",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "a4d4a925-f9a2-4ba4-897b-2f82c54f83d8",
          "package_name": "pytest测试流量包",
          "package_desc": "pytest自动化测试创建的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 39.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "pytest优惠",
          "sale_status": "on_sale",
          "currency": "CNY",
          "country": "CN",
          "psp_product_id": "prod_SlasmYGYBvnEvS",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq3guC53MAl6Wmq5opbJepr",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "a22f526f-a34d-4b2a-a6f6-4e6a9aaf3229",
          "package_name": "RPC测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 29.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlYpXswH5TWKRK",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq1hmC53MAl6WmqWxRUMWjG",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "92e7c9e7-1950-452e-8488-711784d0ef8e",
          "package_name": "测试流量包GIN",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 19.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlYpp1VAJYpLrI",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq1hlC53MAl6Wmq6k1EspdD",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "a65f1502-5515-4d44-9429-3e61edf7e792",
          "package_name": "这是真实数据",
          "package_desc": "这是真实数据",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 125.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SWi3lfOWBSCgdU",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "psp_price_desc": "",
          "product_img": ""
        }
      ],
      "pagination": {
        "total": 15,
        "limit": 50,
        "offset": 0,
        "remaining": 0
      }
    }
2025/07/30 14:36:42 --- List All Packages with Filters (External Gin HTTP) ---
2025/07/30 14:36:42 URL: http://ny10wt9045294.vicp.fun:25639/api/v1/pay-service/store-service/packages?currency=USD&limit=20&offset=0
2025/07/30 14:36:42 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************.cw4rxR-vAEk-xjK4Q4zLq0j7w3Za8EdsuqKHkqv1t-c x-trace-id:test-**********015002900-**********]
2025/07/30 14:36:42 Response Status: 200
2025/07/30 14:36:42 Response Body: {
      "packages": [
        {
          "package_id": "4ca99865-4616-4194-af23-554290c58c15",
          "package_name": "RPC更新后的测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 124.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_Sm1gxHY6TpNmR1",
          "psp_product_desc": "",
          "psp_price_id": "price_1RqTd0C53MAl6WmqGoWhTPOh",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "cfeb107d-935f-498e-ba1e-694259f867ef",
          "package_name": "RPC更新后的测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 124.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfzrbIN7jZQmv",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq8dGC53MAl6WmqIxjKOJ0S",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "3829b0a6-3f24-4a92-8af8-166593853f48",
          "package_name": "更新后的测试流量包",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 24.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfVgN67pzHvKO",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq8AgC53MAl6WmqDpb4uRMg",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "70579727-8354-46db-ae9d-6c3c3d3b2324",
          "package_name": "RPC更新后的测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 124.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfUCd639uIllj",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq89EC53MAl6WmqiO6QMx1A",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "9dd69850-878d-462b-803a-8340de16e41d",
          "package_name": "更新后的测试流量包",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 24.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlfUX7NuQFJjh0",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq89BC53MAl6WmqIpzrl4Yl",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "79d4d6ee-9cd8-4c7d-8447-b84fbafa6d9c",
          "package_name": "测试流量包GIN",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 19.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleiBQSSXQTZ4i",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq7OrC53MAl6WmqS5JQIarV",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "5490af08-49c0-4732-96fa-fa411725c09c",
          "package_name": "Test_Package_1GB",
          "package_desc": "Test data package",
          "package_type": "traffic",
          "entitlement": 1024,
          "entitlement_desc": "1GB data",
          "price": 9.99,
          "original_price": null,
          "discount_start_time": "2025-01-01T08:00:00+08:00",
          "discount_end_time": "2025-01-02T08:00:00+08:00",
          "discount_desc": "Test discount",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleNjyyVfYdAFp",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq75CC53MAl6WmqJ1L2PrMk",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "b0b427a0-2b5f-45c6-9450-d49d1cd6c876",
          "package_name": "RPC更新后的测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 124.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleDhg8ZZI15zj",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq6vVC53MAl6WmqYxS3dnxB",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "100f3dd9-2b06-4af7-ab75-51f69427e22e",
          "package_name": "更新后的测试流量包",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 24.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SleDddaMGUabUQ",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq6vTC53MAl6WmqTkmCgEFb",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "9da2481e-5d73-4621-aa9b-706c7f22e4ef",
          "package_name": "RPC测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 29.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_Sle1DdqNy9Sm7b",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq6jLC53MAl6WmqdCNbU2n9",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "ef59713c-3943-4efd-be88-0f9c43ba1f30",
          "package_name": "测试流量包GIN",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 19.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_Sle1aEssOST7HY",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq6jJC53MAl6WmqgzEKq7mf",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "a22f526f-a34d-4b2a-a6f6-4e6a9aaf3229",
          "package_name": "RPC测试流量包",
          "package_desc": "用于RPC API测试的流量包",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 29.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlYpXswH5TWKRK",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq1hmC53MAl6WmqWxRUMWjG",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "92e7c9e7-1950-452e-8488-711784d0ef8e",
          "package_name": "测试流量包GIN",
          "package_desc": "用于API测试的流量包",
          "package_type": "traffic",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 19.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SlYpp1VAJYpLrI",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rq1hlC53MAl6Wmq6k1EspdD",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "a65f1502-5515-4d44-9429-3e61edf7e792",
          "package_name": "这是真实数据",
          "package_desc": "这是真实数据",
          "package_type": "traffic",
          "entitlement": 200,
          "entitlement_desc": "200GB流量",
          "price": 125.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "psp_product_id": "prod_SWi3lfOWBSCgdU",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "psp_price_desc": "",
          "product_img": ""
        }
      ],
      "pagination": {
        "total": 14,
        "limit": 20,
        "offset": 0,
        "remaining": 0
      }
    }
2025/07/30 14:36:42 --- Create Order (External Gin HTTP) ---
2025/07/30 14:36:42 URL: http://ny10wt9045294.vicp.fun:25639/api/v1/pay-service/order-service/orders
2025/07/30 14:36:42 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************.cw4rxR-vAEk-xjK4Q4zLq0j7w3Za8EdsuqKHkqv1t-c x-trace-id:test-**********110490200-**********]
2025/07/30 14:36:42 Request Body: {ProductID:4ca99865-4616-4194-af23-554290c58c15 ProductDesc:Buy a65f1502-5515-4d44-9429-3e61edf7e792 Quantity:1 Currency:USD PSPProvider:stripe}
2025/07/30 14:36:42 Response Status: 200
2025/07/30 14:36:42 Response Body: {
      "order_id": "202507301436430903STRIPE1950445433314611200",
      "checkout_url": "https://checkout.stripe.com/c/pay/cs_test_a1UbGx8YESNiAtU6OvA4urfeKuvdmF8j4n6uBEgvZ0IVhB6ljUw2A6DaJP#fidkdWxOYHwnPyd1blpxYHZxWjA0V2dhc0ZGMDZIRGkzUmh0a2loPV1zM3NjUWNjfVJHbGB%2FXDFkbWM0cW90QFFzVjJ%2FbDNfRnI0NmRoVmFpMnJUdXxidUZxPUJJQ0lpY0dEQW18f0tBZFJxNTVLV2hsbFxQSScpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl",
      "amount": 124.99,
      "currency": "USD",
      "expires_at": "2025-07-31T14:36:44.065682655+08:00"
    }
2025/07/30 14:36:42 Order Details - ID: 202507301436430903STRIPE1950445433314611200, Amount: 124.99 USD, Checkout URL: https://checkout.stripe.com/c/pay/cs_test_a1UbGx8YESNiAtU6OvA4urfeKuvdmF8j4n6uBEgvZ0IVhB6ljUw2A6DaJP#fidkdWxOYHwnPyd1blpxYHZxWjA0V2dhc0ZGMDZIRGkzUmh0a2loPV1zM3NjUWNjfVJHbGB%2FXDFkbWM0cW90QFFzVjJ%2FbDNfRnI0NmRoVmFpMnJUdXxidUZxPUJJQ0lpY0dEQW18f0tBZFJxNTVLV2hsbFxQSScpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl
