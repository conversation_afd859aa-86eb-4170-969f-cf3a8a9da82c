package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"payment-backend/internal/domain"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"
	"payment-backend/internal/service"
)

// PointHandler 积分处理器
type PointHandler struct {
	pointService service.PointService
	logger       logger.Logger
}

// NewPointHandler 创建积分处理器
func NewPointHandler(pointService service.PointService, logger logger.Logger) *PointHandler {
	return &PointHandler{
		pointService: pointService,
		logger:       logger,
	}
}

// ChargePointsRequest 充值积分请求
type ChargePointsRequest struct {
	Points float32 `json:"points" binding:"required,min=1,max=1000000" example:"100.5"`  // 积分数
	Reason string  `json:"reason" binding:"required,min=1,max=88" example:"订单支付奖励"` // 充值原因
}

// ChargePointsResponse 充值积分响应
type ChargePointsResponse struct {
	Success bool   `json:"success" example:"true"`           // 是否成功
	Message string `json:"message" example:"积分充值成功"`        // 响应消息
}

// ChargePoints 充值积分
// @Summary 充值积分
// @Description 调用用户服务为用户充值积分，需要用户认证
// @Tags 积分管理
// @Accept json
// @Produce json
// @Param x-user-id header string true "用户ID" example("user123")
// @Param x-role header string true "用户角色" example("customer")
// @Param x-country header string false "用户国家" example("US")
// @Param request body ChargePointsRequest true "充值积分请求"
// @Success 200 {object} ChargePointsResponse "积分充值成功"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误, code=10001"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误, code=10003"
// @Router /api/v1/pay-service/point-service/charge [post]
func (h *PointHandler) ChargePoints(c *gin.Context) {
	// 获取用户上下文
	userContext, err := middleware.MustGetUserContext(c)
	if err != nil {
		h.logger.Error("Failed to get user context", zap.Error(err))
		c.JSON(http.StatusUnauthorized, domain.ErrorResponse{
			Code:    domain.ErrCodeUnauthorized,
			Message: "用户认证失败",
		})
		return
	}

	// 解析请求
	var req ChargePointsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", zap.Error(err))
		c.JSON(http.StatusBadRequest, domain.ErrorResponse{
			Code:    domain.ErrCodeInvalidRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取请求上下文
	requestContext, exists := c.Get(middleware.RequestContextKey)
	if !exists {
		h.logger.Error("Request context not found")
		c.JSON(http.StatusInternalServerError, domain.ErrorResponse{
			Code:    domain.ErrCodeInternalError,
			Message: "请求上下文缺失",
		})
		return
	}

	reqCtx := requestContext.(*middleware.RequestContext)

	// 创建带用户信息的上下文
	ctx := h.pointService.WithUserContext(
		c.Request.Context(),
		userContext.UserID,
		userContext.Country,
		reqCtx.TraceId,
	)

	h.logger.Info("ChargePoints HTTP request",
		zap.String("user_id", userContext.UserID),
		zap.String("role", userContext.Role),
		zap.String("country", userContext.Country),
		zap.String("trace_id", reqCtx.TraceId),
		zap.Float32("points", req.Points),
		zap.String("reason", req.Reason))

	// 调用积分服务
	if err := h.pointService.ChargePoints(ctx, req.Points, req.Reason); err != nil {
		h.logger.Error("Failed to charge points",
			zap.String("user_id", userContext.UserID),
			zap.Float32("points", req.Points),
			zap.String("reason", req.Reason),
			zap.Error(err))

		c.JSON(http.StatusInternalServerError, domain.ErrorResponse{
			Code:    domain.ErrCodeInternalError,
			Message: "积分充值失败: " + err.Error(),
		})
		return
	}

	h.logger.Info("Points charged successfully via HTTP",
		zap.String("user_id", userContext.UserID),
		zap.Float32("points", req.Points),
		zap.String("reason", req.Reason))

	c.JSON(http.StatusOK, ChargePointsResponse{
		Success: true,
		Message: "积分充值成功",
	})
}
